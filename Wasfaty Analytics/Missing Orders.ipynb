#%%
# Install required libraries if not already installed
# !pip install --upgrade pip
# !pip install psycopg2-binary sqlalchemy pandas python-dotenv --quiet

import pandas as pd
# Import our custom data module
import sys
import os

sys.path.append(os.path.dirname(os.getcwd()))
from data import Settings, get_leads_data, get_todays_leads
from data import get_activities_data

#%%
# Load settings from environment variables or .env file
# Create a .env file based on .env.example and place it in the same directory
settings = Settings(env_file='.env')  # You can specify a custom path if needed
db_config = settings.get_db_config()



# todays un available leads
status = ['unavailable', 'partially-available']
date = '2025-06-12'

# Example: Extract leads data (limit to 10 rows initially)
leads_df = get_leads_data(db_config,
                          statuses=status,
                          start_date=date,
                          limit=1000
                          )
print("Leads data preview:")
leads_df.head(10)

#%%
# Create a config for wasfatydb (activities DB)
wasfatydb_config = {
    "db_host": "************",  # Update if different for wasfatydb
    "db_port": "5432",
    "db_name": "wasfatydb",      # Use the correct DB name for activities
    "db_user": "product_service_user",
    "db_pass": "_&kJCD>>LJ<oTkUp"
}

# Fetch activities for the leads in leads_df
lead_ids = leads_df['order_number'].unique().tolist()

activities_df = get_activities_data(
    wasfatydb_config,
    lead_ids=lead_ids,  # list of order numbers from your leads_df
    limit=10000
)
#%%
# Group by trade code and count the number of activities per trade code
tradecode_col = "Trade Drugs - InternalId__drugCode"
top_activities_by_tradecode = (
    activities_df.groupby(tradecode_col)
    .size()
    .reset_index(name='activity_count')
    .sort_values('activity_count', ascending=False)
    .head(100)
)
top_activities_by_tradecode
#%%
# Join leads_df and activities_df on order_number/orderNumber
joined_df = pd.merge(
    leads_df,
    activities_df,
    left_on='order_number',
    right_on='orderNumber',
    how='left',
    suffixes=('_lead', '_activity')
)

# Preview the joined DataFrame
joined_df.head(10)
#%%
joined_df.to_csv('activities.csv', index=False)

#%%
# Find the top 20 generic codes associated with lost orders
generic_code_col = "Activities - OrderId__genericCode"
top_generic_codes = (
    joined_df[generic_code_col]
    .value_counts()
    .head(20)
    .reset_index()
    .rename(columns={"index": "generic_code", generic_code_col: "lost_order_count"})
)
top_generic_codes
#%%
