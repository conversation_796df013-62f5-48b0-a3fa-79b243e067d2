"""
Database module for extracting data from PostgreSQL database.
This module handles connections and data extraction for Wasfaty Leads Analytics.
"""

import pandas as pd
from sqlalchemy import create_engine
from typing import Dict, Optional, Union
import os
from dotenv import load_dotenv


class Settings:
    """
    Configuration settings for database connections.
    Loads from environment variables or uses default values.
    """

    def __init__(self, env_file: Optional[str] = None):
        """
        Initialize settings from environment variables.

        Args:
            env_file: Optional path to .env file
        """
        # Load environment variables from .env file if provided
        if env_file and os.path.exists(env_file):
            load_dotenv(env_file)

        # Database connection settings
        self.db_host = os.getenv("DB_HOST", "localhost")
        self.db_port = os.getenv("DB_PORT", "5432")
        self.db_name = os.getenv("DB_NAME", "")
        self.db_user = os.getenv("DB_USER", "")
        self.db_pass = os.getenv("DB_PASSWORD", "")

    def get_db_config(self) -> Dict[str, str]:
        """
        Get database configuration as a dictionary.

        Returns:
            Dictionary with database connection parameters
        """
        return {
            "db_host": self.db_host,
            "db_port": self.db_port,
            "db_name": self.db_name,
            "db_user": self.db_user,
            "db_pass": self.db_pass
        }


def get_db_connection(config: Dict[str, str]):
    """
    Create a database connection using the provided configuration.

    Args:
        config: Dictionary containing db_host, db_port, db_name, db_user, and db_pass

    Returns:
        SQLAlchemy engine object
    """
    conn_str = f"postgresql+psycopg2://{config['db_user']}:{config['db_pass']}@{config['db_host']}:{config['db_port']}/{config['db_name']}"
    return create_engine(conn_str)


def extract_data(engine, query: str) -> pd.DataFrame:
    """
    Extract data from the database using the provided query.

    Args:
        engine: SQLAlchemy engine object
        query: SQL query to execute

    Returns:
        Pandas DataFrame with the query results
    """
    return pd.read_sql(query, engine)


def get_leads_data(config: Dict[str, str],
                   statuses: Optional[list] = None,
                   start_date: Optional[str] = None,
                   end_date: Optional[str] = None,
                   activity_count: Optional[int] = None,
                   limit: Optional[int] = None) -> pd.DataFrame:
    """
    Extract leads data from the database with flexible filtering options.

    Args:
        config: Database configuration dictionary
        statuses: Optional list of availability_status values to filter by
        start_date: Optional start date filter in format 'YYYY-MM-DD'
        end_date: Optional end date filter in format 'YYYY-MM-DD'
        activity_count: Optional filter for specific activity count
        limit: Optional limit on the number of rows to return

    Returns:
        DataFrame containing leads data
    """
    engine = get_db_connection(config)

    # Base query
    query = "SELECT * FROM wasfaty_leads"

    # Build WHERE conditions
    conditions = []

    # Filter by status
    if statuses and len(statuses) > 0:
        status_list = ", ".join([f"'{status}'" for status in statuses])
        conditions.append(f"availability_status IN ({status_list})")

    # Filter by date range
    if start_date:
        conditions.append(f"created_at >= '{start_date}'")

    if end_date:
        conditions.append(f"created_at <= '{end_date}'")

    # Filter by activity count
    if activity_count is not None:
        conditions.append(f"activity_count = {activity_count}")

    # Add WHERE clause if we have conditions
    if conditions:
        query += " WHERE " + " AND ".join(conditions)

    # Add limit if provided
    if limit:
        query += f" LIMIT {limit}"
    print(f"Query",query)
    return extract_data(engine, query)


def get_todays_leads(config: Dict[str, str],
                     statuses: Optional[list] = None,
                     activity_count: Optional[int] = None,
                     limit: Optional[int] = None) -> pd.DataFrame:
    """
    Get leads created today with optional status and activity count filtering.

    Args:
        config: Database configuration dictionary
        statuses: Optional list of availability_status values to filter by
        activity_count: Optional filter for specific activity count
        limit: Optional limit on the number of rows to return

    Returns:
        DataFrame containing today's leads data
    """
    import datetime

    today = datetime.datetime.now().strftime('%Y-%m-%d')

    return get_leads_data(
        config=config,
        statuses=statuses,
        start_date=today,
        activity_count=activity_count,
        limit=limit
    )


def get_activities_data(config: Dict[str, str],
                      start_date: Optional[str] = None,
                      end_date: Optional[str] = None,
                      lead_ids: Optional[list] = None,
                      limit: Optional[int] = None) -> pd.DataFrame:
    """
    Extract activities data from the wasfatydb.activities table with optional filters.

    Args:
        config: Database configuration dictionary for wasfatydb
        start_date: Optional start date filter in format 'YYYY-MM-DD'
        end_date: Optional end date filter in format 'YYYY-MM-DD'
        lead_ids: Optional list of lead IDs to filter by
        limit: Optional limit on the number of rows to return

    Returns:
        DataFrame containing activities data
    """
    engine = get_db_connection(config)
    query = """SELECT "public"."leads"."orderNumber"                 AS "orderNumber",
                      CASE
                          WHEN "Drugs - DrugCode"."sku" IS NULL THEN 'No'
                          ELSE 'Yes'
                          END                                        AS "Is Mapped?",
                      "public"."leads"."erxReference"                AS "erxReference",
                      "public"."leads"."createdAt"                   AS "createdAt",
                      "public"."leads"."branchLicense"               AS "branchLicense",
                      "public"."leads"."status"                      AS "status",
                      "Activities - OrderId"."genericCode"           AS "Activities - OrderId__genericCode",
                      "Trade Drugs - InternalId"."drugCode"          AS "Trade Drugs - InternalId__drugCode",
                      "Trade Drugs - InternalId"."quantity"          AS "Trade Drugs - InternalId__quantity",
                      "Trade Drugs - InternalId"."isAvailable"       AS "Trade Drugs - InternalId__isAvailable",
                      "Trade Drugs - InternalId"."quantityAvailable" AS "Trade Drugs - InternalId__quantityAvailable",
                      "Drugs - DrugCode"."name"                      AS "Drugs - DrugCode__name",
                      "Drugs - DrugCode"."sku"                       AS "Drugs - DrugCode__sku"
               FROM "public"."leads"

                        LEFT JOIN "public"."activities" AS "Activities - OrderId"
                                  ON "public"."leads"."orderId" = "Activities - OrderId"."orderId"
                        LEFT JOIN "public"."trade_drugs" AS "Trade Drugs - InternalId"
                                  ON "Activities - OrderId"."internalId" =
                                     "Trade Drugs - InternalId"."internalActivityId"
                        LEFT JOIN "public"."drugs" AS "Drugs - DrugCode"
                                  ON "Trade Drugs - InternalId"."drugCode" = "Drugs - DrugCode"."code"
               WHERE 
                   "public"."leads"."status" in ('failed','rejected','new','inactive','canceled')
                       
                 AND (
                   "public"."leads"."createdAt" >= timestamp with time zone '2025-05-07 00:00:00.000 +03:00'
                   )
                 AND (
                   "public"."leads"."createdAt" < timestamp with time zone '2025-05-15 00:00:00.000 +03:00'
                   )
               ORDER BY "public"."leads"."createdAt" DESC 
            """
    conditions = []
    if start_date:
        conditions.append(f"created_at >= '{start_date}'")
    if end_date:
        conditions.append(f"created_at <= '{end_date}'")
    if lead_ids and len(lead_ids) > 0:
        lead_id_list = ', '.join([f"'{lid}'" for lid in lead_ids])
        conditions.append(f"lead_id IN ({lead_id_list})")
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    if limit:
        query += f" LIMIT {limit}"
    print(f"Activities Query", query)
    return extract_data(engine, query)
